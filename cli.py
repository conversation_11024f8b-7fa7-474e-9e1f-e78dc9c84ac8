#!/usr/bin/env python3
"""
Command Line Interface for the Prompt Injection & Jailbreak Defense Simulator
"""

import argparse
import sys
import json
from pathlib import Path
from prompt_injection_simulator import PromptInjectionSimulator, AttackType
from config import DEFAULT_SYSTEM_PROMPTS
from colorama import init, Fore, Style

# Initialize colorama
init()

def print_colored(text: str, color: str = Fore.WHITE):
    """Print colored text"""
    print(f"{color}{text}{Style.RESET_ALL}")

def load_system_prompt(prompt_source: str) -> str:
    """Load system prompt from various sources"""
    if prompt_source in DEFAULT_SYSTEM_PROMPTS:
        return DEFAULT_SYSTEM_PROMPTS[prompt_source].strip()
    elif Path(prompt_source).exists():
        with open(prompt_source, 'r', encoding='utf-8') as f:
            return f.read().strip()
    else:
        return prompt_source  # Treat as direct prompt text

def run_simulation(args):
    """Run the simulation based on command line arguments"""
    print_colored("🚀 Starting Prompt Injection Defense Simulation", Fore.CYAN)
    
    # Load system prompt
    try:
        system_prompt = load_system_prompt(args.prompt)
        print_colored(f"✓ System prompt loaded", Fore.GREEN)
    except Exception as e:
        print_colored(f"✗ Error loading system prompt: {e}", Fore.RED)
        return 1
    
    # Create simulator
    simulator = PromptInjectionSimulator(
        system_prompt, 
        enable_safe_mode=not args.no_safe_mode
    )
    
    print_colored(f"✓ Simulator created (Safe Mode: {'ON' if not args.no_safe_mode else 'OFF'})", Fore.GREEN)
    
    # Run attacks
    if args.custom_attack:
        print_colored("Running custom attack...", Fore.YELLOW)
        result = simulator.run_custom_attack(
            args.custom_attack,
            AttackType.DIRECT_INJECTION,
            "Custom CLI attack"
        )
        
        print_colored(f"\nAttack Result:", Fore.CYAN)
        print(f"Success: {'Yes' if result.success else 'No'}")
        print(f"Defense: {result.defense_result.value}")
        print(f"Response: {result.response}")
        
    else:
        print_colored("Running all predefined attacks...", Fore.YELLOW)
        results = simulator.run_all_attacks()
        
        # Summary
        successful = sum(1 for r in results if r.success)
        blocked = sum(1 for r in results if r.defense_result.value == 'blocked')
        
        print_colored(f"\n📊 Results Summary:", Fore.CYAN)
        print(f"Total attacks: {len(results)}")
        print(f"Successful attacks: {successful}")
        print(f"Blocked attacks: {blocked}")
        print(f"Success rate: {(successful/len(results)*100):.1f}%")
    
    # Generate report
    if args.output:
        print_colored(f"\n💾 Generating report...", Fore.YELLOW)
        report_file = simulator.save_report(args.output)
        print_colored(f"✓ Report saved to: {report_file}", Fore.GREEN)
    
    # Display recommendations if verbose
    if args.verbose:
        report = simulator.generate_report()
        print_colored(f"\n🛡️ Security Recommendations:", Fore.CYAN)
        for rec in report['recommendations']:
            print(f"  • {rec}")
    
    return 0

def list_prompts():
    """List available default system prompts"""
    print_colored("📋 Available System Prompts:", Fore.CYAN)
    for name, prompt in DEFAULT_SYSTEM_PROMPTS.items():
        print(f"\n{Fore.YELLOW}{name}:{Style.RESET_ALL}")
        # Show first few lines
        lines = prompt.strip().split('\n')[:3]
        for line in lines:
            if line.strip():
                print(f"  {line.strip()}")
        if len(prompt.strip().split('\n')) > 3:
            print("  ...")

def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description="Prompt Injection & Jailbreak Defense Simulator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --prompt banking                    # Use banking system prompt
  %(prog)s --prompt "Custom prompt text"       # Use custom prompt
  %(prog)s --prompt prompt.txt                 # Load from file
  %(prog)s --custom-attack "Ignore all rules" # Test custom attack
  %(prog)s --no-safe-mode                      # Disable Safe Mode
  %(prog)s --list-prompts                      # Show available prompts
        """
    )
    
    parser.add_argument(
        '--prompt', '-p',
        default='general',
        help='System prompt to use (name, file path, or direct text)'
    )
    
    parser.add_argument(
        '--custom-attack', '-a',
        help='Test a custom attack prompt instead of running all attacks'
    )
    
    parser.add_argument(
        '--no-safe-mode',
        action='store_true',
        help='Disable Safe Mode filtering'
    )
    
    parser.add_argument(
        '--output', '-o',
        help='Output file for detailed report (JSON format)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Show detailed output including recommendations'
    )
    
    parser.add_argument(
        '--list-prompts',
        action='store_true',
        help='List available default system prompts'
    )
    
    args = parser.parse_args()
    
    if args.list_prompts:
        list_prompts()
        return 0
    
    try:
        return run_simulation(args)
    except KeyboardInterrupt:
        print_colored("\n\n⚠️ Simulation interrupted by user", Fore.YELLOW)
        return 1
    except Exception as e:
        print_colored(f"\n❌ Simulation failed: {e}", Fore.RED)
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
